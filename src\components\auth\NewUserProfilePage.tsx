import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import ProtectedRoute from "./ProtectedRoute";
import { motion, AnimatePresence } from "framer-motion";
import {
  Settings, Shield, User, Bell, Key, Palette, ArrowLeft, Save, Edit3,
  Mail, Calendar, MapPin, Phone, Globe, Award, Activity, TrendingUp,
  Download, Star, Clock, CheckCircle, AlertCircle, Loader2
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Link } from "react-router-dom";
import { getUserDisplayName, getUserAvatar } from "@/lib/auth";
import { toast } from "sonner";
import { useUserProfile, RecentActivity } from "@/hooks/useUserProfile";

export const NewUserProfilePage = () => {
  const { user, updateProfile } = useAuth();
  const { profileData, userStats, recentActivity, loading: profileLoading, error, updateUserProfile } = useUserProfile(user);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');
  const [formData, setFormData] = useState({
    full_name: "",
    email: "",
    phone: "",
    location: "",
    bio: "",
    user_type: "",
  });

  // Initialize form data when user and profile data are loaded
  useEffect(() => {
    if (user && profileData) {
      setFormData({
        full_name: profileData.name || getUserDisplayName(user),
        email: user.email || "",
        phone: profileData.phone || user.user_metadata?.phone || "",
        location: profileData.country || user.user_metadata?.location || "",
        bio: user.user_metadata?.bio || "",
        user_type: profileData.user_type || "user",
      });
    }
  }, [user, profileData]);

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // Update the users table
      const { error: profileError } = await updateUserProfile({
        name: formData.full_name,
        phone: formData.phone,
        country: formData.location,
      });

      if (profileError) {
        toast.error(profileError);
        return;
      }

      // Update auth user metadata
      const { error: authError } = await updateProfile({
        data: {
          full_name: formData.full_name,
          phone: formData.phone,
          location: formData.location,
          bio: formData.bio,
        }
      });

      if (!authError) {
        setIsEditing(false);
        toast.success("Profile updated successfully!");
      } else {
        toast.error("Failed to update auth profile");
      }
    } catch (err) {
      console.error('Error updating profile:', err);
      toast.error("Failed to update profile");
    } finally {
      setIsLoading(false);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.4 }
    }
  };

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'settings', label: 'Settings', icon: Settings },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'activity', label: 'Activity', icon: Activity },
  ];

  // Show loading state
  if (!user || profileLoading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-[#111111] text-gray-300 flex items-center justify-center">
          <div className="flex items-center gap-3">
            <Loader2 className="w-6 h-6 animate-spin text-purple-400" />
            <span className="text-lg">Loading profile...</span>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  // Show error state
  if (error) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-[#111111] text-gray-300 flex items-center justify-center">
          <div className="text-center">
            <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Error Loading Profile</h2>
            <p className="text-gray-400 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()} className="bg-purple-600 hover:bg-purple-700">
              Retry
            </Button>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  const userName = getUserDisplayName(user);
  const userAvatar = getUserAvatar(user);

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-[#111111] text-gray-300 relative overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0 z-0 pointer-events-none" style={{
          background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
        }}></div>

        {/* Animated particles */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full bg-purple-500/5"
            style={{
              width: Math.random() * 100 + 50,
              height: Math.random() * 100 + 50,
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -200, 0],
              x: [0, Math.random() * 100 - 50, 0],
              opacity: [0, 0.3, 0],
            }}
            transition={{
              duration: Math.random() * 20 + 15,
              repeat: Infinity,
              delay: Math.random() * 10,
            }}
          />
        ))}

        <div className="relative z-10 py-8 px-4">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="max-w-7xl mx-auto space-y-8"
          >
            {/* Header */}
            <motion.div variants={itemVariants} className="flex items-center justify-between mb-12">
              <div className="flex items-center gap-4">
                <Link to="/">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-gray-400 hover:text-white hover:bg-gray-800/50 transition-all duration-300 border border-gray-700/50 hover:border-purple-400/50"
                  >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Home
                  </Button>
                </Link>
                <div>
                  <h1 className="text-4xl font-bold text-white mb-2">Profile Dashboard</h1>
                  <p className="text-gray-400">Manage your account settings and preferences</p>
                </div>
              </div>
            </motion.div>

            {/* Hero Profile Section */}
            <motion.div variants={itemVariants} className="mb-12">
              <div className="bg-gradient-to-r from-purple-600/20 via-purple-500/10 to-purple-700/20 rounded-2xl p-8 border border-gray-700/50 backdrop-blur-lg relative overflow-hidden">
                <div className="absolute inset-0 bg-[url('/patterns/grid.svg')] opacity-5"></div>

                <div className="relative z-10 flex flex-col lg:flex-row items-center gap-8">
                  <div className="relative">
                    <div className="w-32 h-32 rounded-full overflow-hidden border-4 border-purple-500/30 shadow-2xl">
                      {userAvatar ? (
                        <img
                          src={userAvatar}
                          alt="User Avatar"
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center text-white font-bold text-4xl">
                          {userName.charAt(0).toUpperCase()}
                        </div>
                      )}
                    </div>
                    <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-[#111111] flex items-center justify-center">
                      <CheckCircle className="w-4 h-4 text-white" />
                    </div>
                  </div>

                  <div className="flex-1 text-center lg:text-left">
                    <h2 className="text-3xl font-bold text-white mb-2">{userName}</h2>
                    <p className="text-purple-400 text-lg mb-4">{user.email}</p>
                    <div className="flex flex-wrap items-center justify-center lg:justify-start gap-4 mb-6">
                      <div className="flex items-center gap-2 bg-[#1a1a1a] px-3 py-1 rounded-full border border-gray-700/50">
                        <div className={`w-2 h-2 rounded-full ${
                          userStats?.accountStatus === 'active' ? 'bg-green-500 animate-pulse' :
                          userStats?.accountStatus === 'blocked' ? 'bg-red-500' : 'bg-yellow-500'
                        }`}></div>
                        <span className={`text-sm capitalize ${
                          userStats?.accountStatus === 'active' ? 'text-green-400' :
                          userStats?.accountStatus === 'blocked' ? 'text-red-400' : 'text-yellow-400'
                        }`}>
                          {userStats?.accountStatus || 'Loading...'}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 bg-[#1a1a1a] px-3 py-1 rounded-full border border-gray-700/50">
                        <Calendar className="w-4 h-4 text-purple-400" />
                        <span className="text-gray-300 text-sm">Joined {userStats?.joinDate || 'Loading...'}</span>
                      </div>
                      <div className="flex items-center gap-2 bg-[#1a1a1a] px-3 py-1 rounded-full border border-gray-700/50">
                        <Clock className="w-4 h-4 text-blue-400" />
                        <span className="text-gray-300 text-sm">Last login {userStats?.lastLogin || 'Loading...'}</span>
                      </div>
                    </div>

                    <Button
                      onClick={() => setIsEditing(!isEditing)}
                      className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-full transition-all duration-300 hover:scale-105"
                    >
                      <Edit3 className="w-4 h-4 mr-2" />
                      {isEditing ? "Cancel Edit" : "Edit Profile"}
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Stats Cards */}
            <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
              <motion.div variants={cardVariants}>
                <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 text-center backdrop-blur-lg hover:border-purple-400/50 transition-all duration-300 hover:-translate-y-2">
                  <div className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-purple-900/30 to-purple-800/20 rounded-full mb-4 mx-auto">
                    <Activity className="h-6 w-6 text-purple-400" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-white">Total Operations</h3>
                  <p className="text-2xl font-bold text-purple-400">
                    {userStats?.totalOperations ?? <Loader2 className="w-6 h-6 animate-spin mx-auto" />}
                  </p>
                  <p className="text-gray-400 text-sm">Device operations</p>
                </div>
              </motion.div>

              <motion.div variants={cardVariants}>
                <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 text-center backdrop-blur-lg hover:border-blue-400/50 transition-all duration-300 hover:-translate-y-2">
                  <div className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-blue-900/30 to-blue-800/20 rounded-full mb-4 mx-auto">
                    <Shield className="h-6 w-6 text-blue-400" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-white">Security Score</h3>
                  <p className="text-2xl font-bold text-blue-400">
                    {userStats?.securityScore ? `${userStats.securityScore}%` : <Loader2 className="w-6 h-6 animate-spin mx-auto" />}
                  </p>
                  <p className="text-gray-400 text-sm">Account security</p>
                </div>
              </motion.div>

              <motion.div variants={cardVariants}>
                <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 text-center backdrop-blur-lg hover:border-green-400/50 transition-all duration-300 hover:-translate-y-2">
                  <div className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-green-900/30 to-green-800/20 rounded-full mb-4 mx-auto">
                    <TrendingUp className="h-6 w-6 text-green-400" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-white">Profile Views</h3>
                  <p className="text-2xl font-bold text-green-400">
                    {userStats?.profileViews ? userStats.profileViews.toLocaleString() : <Loader2 className="w-6 h-6 animate-spin mx-auto" />}
                  </p>
                  <p className="text-gray-400 text-sm">Total views</p>
                </div>
              </motion.div>

              <motion.div variants={cardVariants}>
                <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 text-center backdrop-blur-lg hover:border-yellow-400/50 transition-all duration-300 hover:-translate-y-2">
                  <div className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-yellow-900/30 to-yellow-800/20 rounded-full mb-4 mx-auto">
                    <Star className="h-6 w-6 text-yellow-400" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2 text-white">User Rating</h3>
                  <p className="text-2xl font-bold text-yellow-400">
                    {userStats?.userRating ? userStats.userRating.toFixed(1) : <Loader2 className="w-6 h-6 animate-spin mx-auto" />}
                  </p>
                  <p className="text-gray-400 text-sm">Average rating</p>
                </div>
              </motion.div>
            </motion.div>

            {/* Tabs Navigation */}
            <motion.div variants={itemVariants} className="mb-8">
              <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-2 backdrop-blur-lg">
                <div className="flex flex-wrap gap-2">
                  {tabs.map((tab) => {
                    const Icon = tab.icon;
                    return (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-300 ${
                          activeTab === tab.id
                            ? 'bg-purple-600 text-white shadow-lg'
                            : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
                        }`}
                      >
                        <Icon className="w-4 h-4" />
                        <span className="font-medium">{tab.label}</span>
                      </button>
                    );
                  })}
                </div>
              </div>
            </motion.div>

            {/* Tab Content */}
            <AnimatePresence mode="wait">
              {activeTab === 'profile' && (
                <motion.div
                  key="profile"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="grid grid-cols-1 lg:grid-cols-2 gap-8"
                >
                  {/* Profile Information */}
                  <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg">
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-xl font-semibold text-white flex items-center gap-2">
                        <User className="w-5 h-5 text-purple-400" />
                        Profile Information
                      </h3>
                    </div>

                    <AnimatePresence>
                      {isEditing ? (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          className="space-y-6"
                        >
                          <div className="space-y-2">
                            <Label htmlFor="full_name" className="text-gray-300">
                              Full Name
                            </Label>
                            <Input
                              id="full_name"
                              value={formData.full_name}
                              onChange={(e) => setFormData({ ...formData, full_name: e.target.value })}
                              className="bg-gray-800 border-gray-600 text-white focus:border-purple-400"
                              placeholder="Enter your full name"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="email" className="text-gray-300">
                              Email
                            </Label>
                            <Input
                              id="email"
                              value={formData.email}
                              disabled
                              className="bg-gray-800 border-gray-600 text-gray-400"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="phone" className="text-gray-300">
                              Phone Number
                            </Label>
                            <Input
                              id="phone"
                              value={formData.phone}
                              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                              className="bg-gray-800 border-gray-600 text-white focus:border-purple-400"
                              placeholder="Enter your phone number"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="location" className="text-gray-300">
                              Location
                            </Label>
                            <Input
                              id="location"
                              value={formData.location}
                              onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                              className="bg-gray-800 border-gray-600 text-white focus:border-purple-400"
                              placeholder="Enter your location"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="bio" className="text-gray-300">
                              Bio
                            </Label>
                            <textarea
                              id="bio"
                              value={formData.bio}
                              onChange={(e) => setFormData({ ...formData, bio: e.target.value })}
                              className="w-full bg-gray-800 border border-gray-600 text-white rounded-md px-3 py-2 focus:border-purple-400 focus:outline-none resize-none"
                              rows={3}
                              placeholder="Tell us about yourself"
                            />
                          </div>

                          <div className="flex gap-3 pt-4">
                            <Button
                              onClick={handleSave}
                              disabled={isLoading}
                              className="bg-purple-600 hover:bg-purple-700 text-white flex-1"
                            >
                              <Save className="w-4 h-4 mr-2" />
                              {isLoading ? "Saving..." : "Save Changes"}
                            </Button>
                            <Button
                              onClick={() => setIsEditing(false)}
                              variant="outline"
                              className="border-gray-600 text-gray-300 hover:bg-gray-800"
                            >
                              Cancel
                            </Button>
                          </div>
                        </motion.div>
                      ) : (
                        <motion.div
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          className="space-y-4"
                        >
                          <div className="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg">
                            <User className="w-5 h-5 text-purple-400" />
                            <div>
                              <p className="text-gray-400 text-sm">Full Name</p>
                              <p className="text-white font-medium">{formData.full_name || 'Not set'}</p>
                            </div>
                          </div>

                          <div className="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg">
                            <Mail className="w-5 h-5 text-blue-400" />
                            <div>
                              <p className="text-gray-400 text-sm">Email</p>
                              <p className="text-white font-medium">{formData.email}</p>
                            </div>
                          </div>

                          <div className="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg">
                            <Phone className="w-5 h-5 text-green-400" />
                            <div>
                              <p className="text-gray-400 text-sm">Phone</p>
                              <p className="text-white font-medium">{formData.phone || 'Not set'}</p>
                            </div>
                          </div>

                          <div className="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg">
                            <MapPin className="w-5 h-5 text-red-400" />
                            <div>
                              <p className="text-gray-400 text-sm">Location</p>
                              <p className="text-white font-medium">{formData.location || 'Not set'}</p>
                            </div>
                          </div>

                          {formData.bio && (
                            <div className="flex items-start gap-3 p-3 bg-gray-800/50 rounded-lg">
                              <Globe className="w-5 h-5 text-yellow-400 mt-1" />
                              <div>
                                <p className="text-gray-400 text-sm">Bio</p>
                                <p className="text-white font-medium">{formData.bio}</p>
                              </div>
                            </div>
                          )}

                          {profileData && (
                            <>
                              <div className="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg">
                                <Star className="w-5 h-5 text-blue-400" />
                                <div>
                                  <p className="text-gray-400 text-sm">Account Type</p>
                                  <p className="text-white font-medium capitalize">{profileData.user_type || 'User'}</p>
                                </div>
                              </div>

                              <div className="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg">
                                <Award className="w-5 h-5 text-green-400" />
                                <div>
                                  <p className="text-gray-400 text-sm">Credits</p>
                                  <p className="text-white font-medium">{profileData.credits || '0'} credits</p>
                                </div>
                              </div>
                            </>
                          )}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>

                  {/* Account Activity */}
                  <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg">
                    <h3 className="text-xl font-semibold text-white flex items-center gap-2 mb-6">
                      <Activity className="w-5 h-5 text-purple-400" />
                      Account Activity
                    </h3>

                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-green-500/20 rounded-full flex items-center justify-center">
                            <CheckCircle className="w-5 h-5 text-green-400" />
                          </div>
                          <div>
                            <p className="text-white font-medium">Account Created</p>
                            <p className="text-gray-400 text-sm">{userStats?.joinDate || 'Loading...'}</p>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center">
                            <Clock className="w-5 h-5 text-blue-400" />
                          </div>
                          <div>
                            <p className="text-white font-medium">Last Login</p>
                            <p className="text-gray-400 text-sm">{userStats?.lastLogin || 'Loading...'}</p>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-purple-500/20 rounded-full flex items-center justify-center">
                            <Activity className="w-5 h-5 text-purple-400" />
                          </div>
                          <div>
                            <p className="text-white font-medium">Total Operations</p>
                            <p className="text-gray-400 text-sm">{userStats?.totalOperations || 0} operations</p>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                            userStats?.accountStatus === 'active' ? 'bg-green-500/20' :
                            userStats?.accountStatus === 'blocked' ? 'bg-red-500/20' : 'bg-yellow-500/20'
                          }`}>
                            <Shield className={`w-5 h-5 ${
                              userStats?.accountStatus === 'active' ? 'text-green-400' :
                              userStats?.accountStatus === 'blocked' ? 'text-red-400' : 'text-yellow-400'
                            }`} />
                          </div>
                          <div>
                            <p className="text-white font-medium">Account Status</p>
                            <p className={`text-sm capitalize ${
                              userStats?.accountStatus === 'active' ? 'text-green-400' :
                              userStats?.accountStatus === 'blocked' ? 'text-red-400' : 'text-yellow-400'
                            }`}>
                              {userStats?.accountStatus || 'Loading...'}
                            </p>
                          </div>
                        </div>
                      </div>

                      {profileData && (
                        <div className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center">
                              <Star className="w-5 h-5 text-blue-400" />
                            </div>
                            <div>
                              <p className="text-white font-medium">Credits</p>
                              <p className="text-blue-400 text-sm">{profileData.credits || '0'} credits</p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </motion.div>
              )}

              {activeTab === 'settings' && (
                <motion.div
                  key="settings"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="grid grid-cols-1 lg:grid-cols-2 gap-8"
                >
                  <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg">
                    <h3 className="text-xl font-semibold text-white flex items-center gap-2 mb-6">
                      <Settings className="w-5 h-5 text-purple-400" />
                      General Settings
                    </h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg">
                        <div>
                          <p className="text-white font-medium">Email Notifications</p>
                          <p className="text-gray-400 text-sm">Receive updates via email</p>
                        </div>
                        <div className="w-12 h-6 bg-purple-600 rounded-full relative cursor-pointer">
                          <div className="w-5 h-5 bg-white rounded-full absolute top-0.5 right-0.5 transition-all"></div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg">
                        <div>
                          <p className="text-white font-medium">Push Notifications</p>
                          <p className="text-gray-400 text-sm">Get notified about important updates</p>
                        </div>
                        <div className="w-12 h-6 bg-gray-600 rounded-full relative cursor-pointer">
                          <div className="w-5 h-5 bg-white rounded-full absolute top-0.5 left-0.5 transition-all"></div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg">
                        <div>
                          <p className="text-white font-medium">Dark Mode</p>
                          <p className="text-gray-400 text-sm">Use dark theme</p>
                        </div>
                        <div className="w-12 h-6 bg-purple-600 rounded-full relative cursor-pointer">
                          <div className="w-5 h-5 bg-white rounded-full absolute top-0.5 right-0.5 transition-all"></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg">
                    <h3 className="text-xl font-semibold text-white flex items-center gap-2 mb-6">
                      <Palette className="w-5 h-5 text-purple-400" />
                      Appearance
                    </h3>
                    <div className="space-y-4">
                      <div className="p-4 bg-gray-800/50 rounded-lg">
                        <p className="text-white font-medium mb-3">Theme Color</p>
                        <div className="flex gap-3">
                          <div className="w-8 h-8 bg-purple-500 rounded-full cursor-pointer ring-2 ring-purple-400 ring-offset-2 ring-offset-[#1a1a1a]"></div>
                          <div className="w-8 h-8 bg-blue-500 rounded-full cursor-pointer"></div>
                          <div className="w-8 h-8 bg-green-500 rounded-full cursor-pointer"></div>
                          <div className="w-8 h-8 bg-red-500 rounded-full cursor-pointer"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              {activeTab === 'security' && (
                <motion.div
                  key="security"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="grid grid-cols-1 lg:grid-cols-2 gap-8"
                >
                  <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg">
                    <h3 className="text-xl font-semibold text-white flex items-center gap-2 mb-6">
                      <Shield className="w-5 h-5 text-purple-400" />
                      Security Settings
                    </h3>
                    <div className="space-y-4">
                      <div className="p-4 bg-gray-800/50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <p className="text-white font-medium">Password</p>
                          <Button size="sm" className="bg-purple-600 hover:bg-purple-700">
                            Change
                          </Button>
                        </div>
                        <p className="text-gray-400 text-sm">Last changed 30 days ago</p>
                      </div>

                      <div className="p-4 bg-gray-800/50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <p className="text-white font-medium">Two-Factor Authentication</p>
                          <Button size="sm" variant="outline" className="border-gray-600">
                            Enable
                          </Button>
                        </div>
                        <p className="text-gray-400 text-sm">Add an extra layer of security</p>
                      </div>

                      <div className="p-4 bg-gray-800/50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <p className="text-white font-medium">Login Alerts</p>
                          <div className="w-12 h-6 bg-purple-600 rounded-full relative cursor-pointer">
                            <div className="w-5 h-5 bg-white rounded-full absolute top-0.5 right-0.5 transition-all"></div>
                          </div>
                        </div>
                        <p className="text-gray-400 text-sm">Get notified of new logins</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg">
                    <h3 className="text-xl font-semibold text-white flex items-center gap-2 mb-6">
                      <Key className="w-5 h-5 text-purple-400" />
                      Active Sessions
                    </h3>
                    <div className="space-y-4">
                      <div className="p-4 bg-gray-800/50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <div>
                            <p className="text-white font-medium">Current Session</p>
                            <p className="text-gray-400 text-sm">Windows • Chrome</p>
                          </div>
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        </div>
                        <p className="text-gray-400 text-xs">Last active: Now</p>
                      </div>

                      <div className="p-4 bg-gray-800/50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <div>
                            <p className="text-white font-medium">Mobile Session</p>
                            <p className="text-gray-400 text-sm">iPhone • Safari</p>
                          </div>
                          <Button size="sm" variant="outline" className="border-red-600 text-red-400 hover:bg-red-600/10">
                            Revoke
                          </Button>
                        </div>
                        <p className="text-gray-400 text-xs">Last active: 2 hours ago</p>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              {activeTab === 'activity' && (
                <motion.div
                  key="activity"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg"
                >
                  <h3 className="text-xl font-semibold text-white flex items-center gap-2 mb-6">
                    <Activity className="w-5 h-5 text-purple-400" />
                    Recent Activity
                  </h3>

                  <div className="space-y-4">
                    {recentActivity.length > 0 ? (
                      recentActivity.map((activity: RecentActivity, index: number) => {
                        const getActivityIcon = (operationType: string) => {
                          switch (operationType.toLowerCase()) {
                            case 'direct unlock':
                              return { icon: Key, color: 'text-green-400' };
                            case 'remove frp':
                            case 'remove frp [brom]':
                            case 'remove frp [dm]':
                              return { icon: Shield, color: 'text-blue-400' };
                            case 'write cert':
                              return { icon: Award, color: 'text-purple-400' };
                            case 'convert csc':
                              return { icon: Settings, color: 'text-yellow-400' };
                            default:
                              return { icon: Activity, color: 'text-gray-400' };
                          }
                        };

                        const { icon: Icon, color } = getActivityIcon(activity.operation_type);

                        return (
                          <motion.div
                            key={index}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className="flex items-center gap-4 p-4 bg-gray-800/50 rounded-lg hover:bg-gray-800/70 transition-colors"
                          >
                            <div className={`w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center`}>
                              <Icon className={`w-5 h-5 ${color}`} />
                            </div>
                            <div className="flex-1">
                              <p className="text-white font-medium">{activity.operation_type}</p>
                              <div className="flex items-center gap-2 text-sm text-gray-400">
                                <span>{activity.time}</span>
                                {activity.model && (
                                  <>
                                    <span>•</span>
                                    <span>{activity.model}</span>
                                  </>
                                )}
                                {activity.status && (
                                  <>
                                    <span>•</span>
                                    <span className={`${
                                      activity.status.toLowerCase() === 'success' ? 'text-green-400' :
                                      activity.status.toLowerCase() === 'failed' ? 'text-red-400' : 'text-yellow-400'
                                    }`}>
                                      {activity.status}
                                    </span>
                                  </>
                                )}
                              </div>
                            </div>
                          </motion.div>
                        );
                      })
                    ) : (
                      <div className="text-center py-8">
                        <Activity className="w-12 h-12 text-gray-600 mx-auto mb-4" />
                        <p className="text-gray-400">No recent activity</p>
                        <p className="text-gray-500 text-sm">Your operations will appear here</p>
                      </div>
                    )}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </div>
    </ProtectedRoute>
  );
};

export default NewUserProfilePage;
