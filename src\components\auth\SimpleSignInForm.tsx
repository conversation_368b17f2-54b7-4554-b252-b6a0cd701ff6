import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Chrome, Eye, EyeOff } from "lucide-react";
import { toast } from "sonner";
import { Link, useNavigate } from "react-router-dom";
import { validateSignInForm, FormErrors } from "@/lib/auth";

export const SimpleSignInForm = () => {
  const { signIn, signInWithProvider, loading } = useAuth();
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const formErrors = validateSignInForm(email, password);
    if (Object.keys(formErrors).length > 0) {
      setErrors(formErrors);
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      const { error } = await signIn(email, password);

      if (!error) {
        navigate("/");
      } else {
        setErrors({ general: error.message });
      }
    } catch (err: any) {
      setErrors({ general: "An unexpected error occurred" });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      const { error } = await signInWithProvider('google');
      if (error) {
        toast.error(error.message);
      }
    } catch (err: any) {
      toast.error("Failed to sign in with Google");
    }
  };

  return (
    <div className="w-full">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
        className="bg-[#1a1a1a] border border-gray-700 rounded-xl p-8 space-y-6"
      >
        <div className="text-center">
          <h1 className="text-2xl font-semibold text-white mb-2">Welcome Back</h1>
          <p className="text-gray-400 text-sm">Sign in to your Pegasus Tool account</p>
        </div>

        {/* Social Sign In Button */}
        <Button
          onClick={handleGoogleSignIn}
          variant="outline"
          className="w-full bg-[#111111] border border-gray-700 text-white hover:bg-[#2a2a2a] hover:border-purple-400 transition-all duration-200 h-11"
          disabled={loading}
        >
          <Chrome className="w-4 h-4 mr-2" />
          Sign in with Google
        </Button>

        {/* Divider */}
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-gray-700" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-[#1a1a1a] px-3 text-gray-400">OR</span>
          </div>
        </div>

        {/* Email/Password Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm text-gray-300 font-medium">
              Email Address
            </Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="bg-[#111111] border border-gray-700 text-white placeholder:text-gray-500 focus:border-purple-400 focus:ring-1 focus:ring-purple-400/20 transition-colors duration-200 h-11"
              placeholder="Enter your email"
              required
            />
            {errors.email && (
              <p className="text-red-400 text-sm mt-1">{errors.email}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="password" className="text-sm text-gray-300 font-medium">
              Password
            </Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="bg-[#111111] border border-gray-700 text-white placeholder:text-gray-500 focus:border-purple-400 focus:ring-1 focus:ring-purple-400/20 transition-colors duration-200 h-11 pr-12"
                placeholder="Enter your password"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-purple-400 transition-colors duration-200"
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
            {errors.password && (
              <p className="text-red-400 text-sm mt-1">{errors.password}</p>
            )}
          </div>

          {errors.general && (
            <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3">
              <p className="text-red-400 text-sm text-center">{errors.general}</p>
            </div>
          )}

          <Button
            type="submit"
            disabled={isSubmitting || loading || !email || !password}
            className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-purple-600/50 text-white font-medium h-11 rounded-lg transition-colors duration-200"
          >
            {isSubmitting ? "Signing in..." : "Sign In"}
          </Button>
        </form>

        {/* Links */}
        <div className="space-y-3 text-center">
          <Link
            to="/forgot-password"
            className="block text-gray-400 hover:text-purple-400 text-sm hover:underline transition-colors duration-200"
          >
            Forgot your password?
          </Link>

          <div className="text-sm text-gray-400">
            Don't have an account?{" "}
            <Link
              to="/sign-up"
              className="text-purple-400 hover:text-purple-300 font-medium hover:underline transition-colors duration-200"
            >
              Create Account
            </Link>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default SimpleSignInForm;
