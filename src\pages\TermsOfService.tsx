import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

const TermsOfService = () => {
  return (
    <div className="min-h-screen bg-gradient-to-b from-black to-purple-900/20">
      <div className="container mx-auto px-4 py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-4xl mx-auto"
        >
          <Link
            to="/"
            className="inline-flex items-center text-purple-400 hover:text-purple-300 mb-8 transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Link>

          <h1 className="text-4xl font-bold text-white mb-8">Terms of Service</h1>

          <div className="prose prose-invert max-w-none">
            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-purple-400 mb-4">1. Acceptance of Terms</h2>
              <p className="text-gray-300 mb-4">
                By accessing and using this website, you accept and agree to be bound by the terms and provision of this agreement.
              </p>
            </section>

            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-purple-400 mb-4">2. Use License</h2>
              <p className="text-gray-300 mb-4">
                Permission is granted to temporarily download one copy of the materials (information or software) on Pegasus Tools website for personal, non-commercial transitory viewing only.
              </p>
              <p className="text-gray-300 mb-4">
                This is the grant of a license, not a transfer of title, and under this license you may not:
              </p>
              <ul className="list-disc pl-6 text-gray-300 mb-4">
                <li>Modify or copy the materials</li>
                <li>Use the materials for any commercial purpose</li>
                <li>Attempt to decompile or reverse engineer any software contained on Pegasus Tools website</li>
                <li>Remove any copyright or other proprietary notations from the materials</li>
                <li>Transfer the materials to another person or "mirror" the materials on any other server</li>
              </ul>
            </section>

            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-purple-400 mb-4">3. Disclaimer</h2>
              <p className="text-gray-300 mb-4">
                The materials on Pegasus Tools website are provided on an 'as is' basis. Pegasus Tool makes no warranties, expressed or implied, and hereby disclaims and negates all other warranties including, without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.
              </p>
            </section>

            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-purple-400 mb-4">4. Limitations</h2>
              <p className="text-gray-300 mb-4">
                In no event shall Pegasus Tool or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use the materials on Pegasus Tools website.
              </p>
            </section>

            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-purple-400 mb-4">5. Revisions and Errata</h2>
              <p className="text-gray-300 mb-4">
                The materials appearing on Pegasus Tools website could include technical, typographical, or photographic errors. Pegasus Tool does not warrant that any of the materials on its website are accurate, complete or current.
              </p>
            </section>

            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-purple-400 mb-4">6. Links</h2>
              <p className="text-gray-300 mb-4">
                Pegasus Tool has not reviewed all of the sites linked to its website and is not responsible for the contents of any such linked site. The inclusion of any link does not imply endorsement by Pegasus Tool of the site.
              </p>
            </section>

            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-purple-400 mb-4">7. Modifications</h2>
              <p className="text-gray-300 mb-4">
                Pegasus Tool may revise these terms of service for its website at any time without notice. By using this website you are agreeing to be bound by the then current version of these terms of service.
              </p>
            </section>

            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-purple-400 mb-4">8. Governing Law</h2>
              <p className="text-gray-300 mb-4">
                These terms and conditions are governed by and construed in accordance with the laws and you irrevocably submit to the exclusive jurisdiction of the courts in that location.
              </p>
            </section>
          </div>

          <div className="mt-12 text-center">
            <Link
              to="/privacy-policy"
              className="text-purple-400 hover:text-purple-300 transition-colors"
            >
              View Privacy Policy
            </Link>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default TermsOfService;
